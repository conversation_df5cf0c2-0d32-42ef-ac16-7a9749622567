<?php
/**
 * FawaNews Debug Script
 * This script helps diagnose issues with wp_remote_get and external API calls
 * on a live WordPress environment.
 */

// Ensure WordPress environment is loaded
if ( ! defined( 'ABSPATH' ) ) {
    define('WP_USE_THEMES', false);
    require_once( dirname( __FILE__ ) . '/../../../wp-load.php' );
}

header('Content-Type: text/plain');

echo "FawaNews Debug Script\n";
echo "=======================\n\n";

// Test wp_remote_get to the target URL
echo "1. Testing direct connection to fawanews.co.uk:\n";
$target_url = 'https://fawanews.co.uk/';
$response = wp_remote_get($target_url, [
    'timeout' => 30, // Increased timeout
    'user-agent' => 'Mozilla/5.0 (WordPress Debug; rv:1.0) Gecko/20010101 Firefox/1.0',
    'sslverify' => false // Keep this false for testing, but ideally should be true
]);

if (is_wp_error($response)) {
    echo "   Error: " . $response->get_error_message() . "\n";
    echo "   Possible causes: Firewall blocking outbound connections, DNS resolution issues, SSL certificate problems.\n";
} else {
    $http_code = wp_remote_retrieve_response_code($response);
    $body_length = strlen(wp_remote_retrieve_body($response));
    echo "   HTTP Code: " . $http_code . "\n";
    echo "   Body Length: " . $body_length . " bytes\n";
    if ($http_code === 200 && $body_length > 1000) {
        echo "   Success: Connection seems to be working.\n";
    } else {
        echo "   Warning: Connection returned unexpected result. HTTP Code: {$http_code}, Body Length: {$body_length}.\n";
    }
}
echo "\n";

// Test CORS Proxies
echo "2. Testing CORS Proxies:\n";
$cors_proxies = [
    'https://api.allorigins.win/get?url=',
    'https://api.codetabs.com/v1/proxy?quest='
];

foreach ($cors_proxies as $proxy) {
    echo "   Testing proxy: " . $proxy . "\n";
    $proxy_url = $proxy . urlencode($target_url);
    $response = wp_remote_get($proxy_url, [
        'timeout' => 30, // Increased timeout
        'user-agent' => 'Mozilla/5.0 (WordPress Debug; rv:1.0) Gecko/20010101 Firefox/1.0',
        'sslverify' => false
    ]);

    if (is_wp_error($response)) {
        echo "      Error: " . $response->get_error_message() . "\n";
        echo "      Possible causes: Proxy blocked, proxy service down, network issues.\n";
    } else {
        $http_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $body_length = strlen($body);
        echo "      HTTP Code: " . $http_code . "\n";
        echo "      Body Length: " . $body_length . " bytes\n";
        if ($http_code === 200 && $body_length > 1000) {
            echo "      Success: Proxy seems to be working.\n";
        } else {
            echo "      Warning: Proxy returned unexpected result. HTTP Code: {$http_code}, Body Length: {$body_length}.\n";
        }
    }
    echo "\n";
}

// Test PHP cURL extension
echo "3. Checking PHP cURL extension:\n";
if (function_exists('curl_init')) {
    echo "   cURL extension is enabled.\n";
} else {
    echo "   cURL extension is NOT enabled. This is required for wp_remote_get to function optimally.\n";
}
echo "\n";

// Test allow_url_fopen
echo "4. Checking allow_url_fopen:\n";
if (ini_get('allow_url_fopen')) {
    echo "   allow_url_fopen is enabled.\n";
} else {
    echo "   allow_url_fopen is NOT enabled. This might affect file_get_contents, though wp_remote_get is preferred.\n";
}
echo "\n";

echo "Debug script finished. Please share the output with me.\n";

?>

