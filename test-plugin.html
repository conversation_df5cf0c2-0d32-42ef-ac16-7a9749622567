<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FawaNews Plugin Test v1.1</title>
</head>
<body>
    <h1>FawaNews Sports Plugin Test</h1>
    <p>Testing the WordPress plugin shortcode output:</p>
    
    <!-- Simulate WordPress shortcode output -->
    <div style="border: 2px solid #ccc; padding: 20px; margin: 20px 0; border-radius: 8px;">
        <h3>Shortcode: [MATCH_SCORES]</h3>
        
        <!-- This would be the output from the WordPress plugin -->
        <style>
        /* Complete Professional CSS - Inline with #FF6344 Primary Color */
        .container, .mubashardev-container, .halwasport-container, .fawanews-container {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            padding: 24px 16px;
            max-width: 1200px;
            margin: 0 auto;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 50vh;
            color: #2d3748;
            line-height: 1.6;
            border-radius: 16px;
        }

        .loading-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        .loader-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #FF6344;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-info p {
            font-size: 18px;
            color: #64748b;
            margin: 0;
            font-weight: 500;
        }

        .tab-bar {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 6px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            position: relative;
            color: #64748b;
            background: transparent;
            border: none;
            outline: none;
        }

        .tab-item:hover {
            background: rgba(255, 99, 68, 0.1);
            color: #FF6344;
            transform: translateY(-1px);
        }

        .tab-item.active {
            background: linear-gradient(135deg, #FF6344 0%, #e55039 100%);
            color: white;
            box-shadow: 0 4px 20px rgba(255, 99, 68, 0.4);
            transform: translateY(-1px);
        }

        .no-matches {
            text-align: center;
            padding: 60px 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            border: 2px dashed #d1d5db;
        }

        .no-matches-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .no-matches-title {
            font-size: 24px;
            font-weight: 700;
            color: #374151;
            margin-bottom: 12px;
        }

        .no-matches-description {
            font-size: 16px;
            color: #6b7280;
            line-height: 1.5;
        }
        </style>

        <div class="fawanews-container" data-plugin-version="1.1.0">
            <div class="loading-info" id="fawanewsLoadingInfo">
                <div class="loader-spinner"></div>
                <p>Loading matches...</p>
            </div>

            <div class="tab-bar" id="fawanewsTabBar" style="display: none;">
                <div class="tab-item" data-filter="yesterday">
                    <span class="tab-text">Yesterday</span>
                </div>
                <div class="tab-item active" data-filter="today">
                    <span class="tab-text">Today</span>
                </div>
                <div class="tab-item" data-filter="tomorrow">
                    <span class="tab-text">Tomorrow</span>
                </div>
            </div>

            <div id="fawanewsStatus"></div>
            <div id="fawanewsDataContainer" class="data-container" style="display: none;">
                <div id="fawanewsDataContent" class="data-content"></div>
            </div>
        </div>

        <script type="text/javascript">
        // Simulate the WordPress plugin JavaScript
        console.log('FawaNews Sports Plugin v1.1.0 loaded - All assets inlined');
        
        // Show demo message after 2 seconds
        setTimeout(function() {
            const loadingInfo = document.getElementById('fawanewsLoadingInfo');
            const dataContainer = document.getElementById('fawanewsDataContainer');
            const dataContent = document.getElementById('fawanewsDataContent');
            const tabBar = document.getElementById('fawanewsTabBar');
            
            if (loadingInfo && dataContainer && dataContent && tabBar) {
                loadingInfo.style.display = 'none';
                dataContent.innerHTML = `
                    <div class="no-matches">
                        <div class="no-matches-icon">✅</div>
                        <div class="no-matches-title">Plugin v1.1.0 Working!</div>
                        <div class="no-matches-description">
                            The WordPress plugin is properly configured with inlined assets. 
                            No external CSS or JS files are loaded - everything is embedded!
                        </div>
                    </div>
                `;
                dataContainer.style.display = 'block';
                tabBar.style.display = 'flex';
            }
        }, 2000);
        </script>
    </div>
    
    <div style="background: #f0f0f0; padding: 15px; border-radius: 8px; margin-top: 20px;">
        <h4>Version 1.1.0 Changes:</h4>
        <ul>
            <li>✅ Removed all external CSS and JS file dependencies</li>
            <li>✅ All assets are now inlined in the shortcode</li>
            <li>✅ No more 404 errors for missing files</li>
            <li>✅ Professional UI with #FF6344 primary color</li>
            <li>✅ Clean design with only league names at bottom</li>
            <li>✅ Responsive design for all screen sizes</li>
            <li>✅ WordPress cache clearing on activation</li>
        </ul>
    </div>
</body>
</html>
