<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test WordPress Plugin v1.1.0</title>
</head>
<body>
    <h1>FawaNews WordPress Plugin Test</h1>
    
    <div style="background: #f0f0f0; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>Testing Plugin Data Fetching</h3>
        <p>This page tests if the WordPress plugin can fetch data correctly.</p>
        
        <button onclick="testDirectBackend()">Test Direct Backend</button>
        <button onclick="testPluginAJAX()">Test Plugin AJAX (Simulated)</button>
        
        <div id="results" style="margin-top: 20px; padding: 15px; background: white; border-radius: 4px; min-height: 100px;">
            <p>Click a button to test...</p>
        </div>
    </div>

    <script>
    async function testDirectBackend() {
        const results = document.getElementById('results');
        results.innerHTML = '<p>Testing direct backend.php...</p>';
        
        try {
            const response = await fetch('backend.php?filter=today');
            const data = await response.json();
            
            results.innerHTML = `
                <h4>Direct Backend Test Results:</h4>
                <p><strong>Success:</strong> ${data.success}</p>
                <p><strong>Data Length:</strong> ${data.data ? data.data.length : 0} characters</p>
                <p><strong>All Matches:</strong> ${data.allMatches ? JSON.stringify(Object.keys(data.allMatches)) : 'None'}</p>
                <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px; max-height: 200px; overflow-y: auto;">${JSON.stringify(data, null, 2)}</pre>
            `;
        } catch (error) {
            results.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
        }
    }
    
    async function testPluginAJAX() {
        const results = document.getElementById('results');
        results.innerHTML = '<p>Testing WordPress plugin AJAX simulation...</p>';
        
        // Simulate what the WordPress plugin would do
        try {
            // First test if we can reach the backend directly
            const backendResponse = await fetch('backend.php?filter=today');
            const backendData = await backendResponse.json();
            
            if (backendData.success) {
                results.innerHTML = `
                    <h4>Plugin AJAX Simulation Results:</h4>
                    <p style="color: green;"><strong>✅ Backend is working!</strong></p>
                    <p><strong>WordPress plugin should be able to fetch this data via HTTP request</strong></p>
                    <p><strong>Total matches found:</strong> ${backendData.allMatches ? Object.values(backendData.allMatches).reduce((sum, arr) => sum + arr.length, 0) : 0}</p>
                    <p><strong>Data structure:</strong></p>
                    <ul>
                        ${backendData.allMatches ? Object.entries(backendData.allMatches).map(([day, matches]) => 
                            `<li>${day}: ${matches.length} matches</li>`
                        ).join('') : '<li>No matches data</li>'}
                    </ul>
                `;
            } else {
                results.innerHTML = `
                    <h4>Plugin AJAX Simulation Results:</h4>
                    <p style="color: red;"><strong>❌ Backend is not working</strong></p>
                    <p>The WordPress plugin will also fail because the backend is not returning data.</p>
                `;
            }
        } catch (error) {
            results.innerHTML = `
                <h4>Plugin AJAX Simulation Results:</h4>
                <p style="color: red;"><strong>❌ Error:</strong> ${error.message}</p>
                <p>The WordPress plugin will fail because it cannot reach the backend.</p>
            `;
        }
    }
    
    // Auto-test on page load
    window.addEventListener('load', () => {
        testDirectBackend();
    });
    </script>
    
    <div style="background: #e8f4f8; padding: 15px; border-radius: 8px; margin-top: 20px;">
        <h4>WordPress Plugin v1.1.0 Status:</h4>
        <ul>
            <li>✅ All assets inlined (no 404 errors)</li>
            <li>✅ Professional UI with #FF6344 colors</li>
            <li>✅ Clean design with transparent backgrounds</li>
            <li>🔄 Data fetching: Testing direct HTTP request to backend.php</li>
            <li>🔄 Fallback: Using class-based processor if HTTP fails</li>
        </ul>
        
        <p><strong>Expected behavior:</strong> The WordPress plugin should make an HTTP request to its own backend.php file to get match data, just like the standalone version works.</p>
    </div>
</body>
</html>
