/* Base Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    color: #2d3748;
    line-height: 1.6;
}

.container,
.mubashardev-container,
.halwasport-container,
.fawanews-container {
    padding: 24px 16px;
    max-width: 1200px;
    margin: 0 auto;
}

.data-container {
    margin-top: 20px;
}

.data-content {
    word-wrap: break-word;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

/* Tab Bar Styles */
.tab-bar {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 6px;
    margin-bottom: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 12px 16px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: 14px;
    position: relative;
    color: #64748b;
}

.tab-item:hover {
    background: rgba(255, 99, 68, 0.1);
    color: #FF6344;
    transform: translateY(-1px);
}

.tab-item.active {
    background: linear-gradient(135deg, #FF6344 0%, #e55039 100%);
    color: white;
    box-shadow: 0 4px 20px rgba(255, 99, 68, 0.4);
    transform: translateY(-1px);
}

.tab-item.active:hover {
    background: linear-gradient(135deg, #e55039 0%, #c44569 100%);
    color: white;
}

.tab-text {
    display: block;
    position: relative;
}

/* Loading Styles */
.loading-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-info p {
    color: #666;
    font-size: 16px;
    margin: 0;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Error and No Data Styles */
.error-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #dc3545;
}

.error-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.error-description {
    font-size: 16px;
    color: #666;
    max-width: 400px;
    line-height: 1.5;
}

.no-matches {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #666;
}

.no-matches-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #6c757d;
}

.no-matches-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.no-matches-description {
    font-size: 16px;
    color: #666;
}

/* Match Cards Styles */
.matches-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 0;
    margin: 0;
}

.match-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
    transition: all 0.4s ease;
    border-radius: 20px 20px 0 0;
}

.match-card[style*="cursor: pointer"]:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 99, 68, 0.3);
}

.match-card[style*="cursor: pointer"]:hover::before {
    background: linear-gradient(90deg, #FF6344, #e55039);
    height: 4px;
}

.match-card[style*="cursor: pointer"]:hover .match-overlay {
    opacity: 1;
    visibility: visible;
}

/* Status-based card styling */
.match-card.status-live::before {
    background: linear-gradient(90deg, #ef4444, #dc2626) !important;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
}

.match-card.status-finished::before {
    background: linear-gradient(90deg, #64748b, #475569) !important;
}

.match-card.status-coming-soon::before {
    background: linear-gradient(90deg, #f59e0b, #d97706) !important;
}

.match-card.status-not-started::before {
    background: linear-gradient(90deg, #10b981, #059669) !important;
}

/* Match Overlay */
.match-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.28);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 20px;
    z-index: 10;
}

.overlay-text {
    color: white;
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    padding: 12px 16px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.status-live .match-overlay {
    background: rgba(0, 0, 0, 0.28);
}

.status-finished .match-overlay {
    background: rgba(0, 0, 0, 0.28);
}

.status-coming-soon .match-overlay {
    background: rgba(0, 0, 0, 0.28);
}

.status-not-started .match-overlay {
    background: rgba(0, 0, 0, 0.28);
}

/* Match Header */
.match-header {
    display: none;
}

/* Match Teams */
.match-teams {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 8px 0;
}

.team {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 35%;
    position: relative;
}

.team-logo {
    width: 64px;
    height: 64px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 99, 68, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.match-card:hover .team-logo {
    transform: scale(1.08) rotate(2deg);
    border-color: rgba(255, 99, 68, 0.3);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.team-logo img {
    width: 48px;
    height: 48px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.team-name {
    font-weight: 600;
    font-size: 13px;
    color: #374151;
    text-align: center;
    margin-bottom: 4px;
    line-height: 1.3;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.team-score {
    display: none;
}

.home-team .team-score {
    right: -30px;
}

.away-team .team-score {
    left: -30px;
}

/* Match Center Status */
.match-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 0 0 auto;
    margin: 0 16px;
    min-width: 120px;
}

.match-time-center {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 12px;
    border-radius: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 2px solid rgba(255, 99, 68, 0.1);
    min-height: 64px;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
}

.match-card:hover .match-time-center {
    border-color: rgba(255, 99, 68, 0.2);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.center-time {
    font-size: 18px;
    font-weight: 700;
    color: #1f2937;
    text-align: center;
    line-height: 1.2;
    letter-spacing: -0.5px;
}

.center-status {
    font-size: 11px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    text-align: center;
    padding: 4px 10px;
    border-radius: 20px;
}

.center-status.live {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    animation: pulse 2s infinite;
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
}

.center-status.upcoming {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
}

.center-status.finished {
    background: linear-gradient(135deg, #64748b, #475569);
    color: white;
    box-shadow: 0 4px 16px rgba(100, 116, 139, 0.3);
}



/* Timer and Status Indicators */
.countdown-timer {
    font-family: 'Courier New', monospace;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    background: #fff;
    padding: 6px 12px;
    border-radius: 8px;
    border: 1px solid #ddd;
    margin-top: 4px;
    min-width: 80px;
    text-align: center;
}

.live-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #dc3545;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    animation: pulse 1.5s infinite;
    text-transform: uppercase;
}

.finished-indicator {
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: #fff;
    background: #6c757d;
    padding: 6px 12px;
    border-radius: 8px;
    margin-top: 4px;
    text-align: center;
    text-transform: uppercase;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

/* Match Info */
.match-info {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid rgba(226, 232, 240, 0.8);
    margin-bottom: 0;
}

.competition {
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 600;
    color: #64748b;
    font-size: 12px;
    text-align: center;
    line-height: 1.4;
}

.broadcast-info {
    display: none;
}

.commentary,
.tv {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #6b7280;
}

/* Simple icons using CSS */
.icon-trophy::before {
    content: "🏆";
    margin-right: 4px;
}

.icon-mic::before {
    content: "🎤";
    margin-right: 4px;
}

.icon-tv::before {
    content: "📺";
    margin-right: 4px;
}

/* Card Loader Overlay */
.card-loader-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 20;
    border-radius: 12px;
}

.card-loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: white;
    text-align: center;
}

.card-loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.card-loader-content p {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

/* Try Again Overlay */
.try-again-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 193, 7, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 20;
    border-radius: 12px;
}

.try-again-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #333;
    text-align: center;
}

.try-again-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.try-again-content p {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: bold;
}

.try-again-content small {
    font-size: 14px;
    opacity: 0.8;
}

/* Responsive Design - Maintaining Same Layout */
.container {
    padding: 20px 0;
    max-width: 1200px;
    margin: 0 auto;
}

/* Large screens (1200px+) */
@media (min-width: 1200px) {
    .match-card {
        padding: 24px;
    }

    .team-logo {
        width: 72px;
        height: 72px;
    }

    .team-logo img {
        width: 56px;
        height: 56px;
    }

    .center-time {
        font-size: 20px;
    }

    .team-name {
        font-size: 14px;
    }

    .competition {
        font-size: 13px;
    }
}

/* Medium screens (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {

    .container,
    .halwasport-container,
    .fawanews-container {
        padding: 20px 12px;
    }

    .match-card {
        padding: 18px;
    }

    .team-logo {
        width: 60px;
        height: 60px;
    }

    .team-logo img {
        width: 44px;
        height: 44px;
    }

    .match-center {
        min-width: 110px;
        margin: 0 12px;
    }

    .match-time-center {
        padding: 14px 10px;
        min-height: 60px;
    }

    .center-time {
        font-size: 16px;
    }

    .team-name {
        font-size: 12px;
    }

    .competition {
        font-size: 11px;
    }

    .commentary,
    .tv {
        font-size: 10px;
    }
}

/* Small screens (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {

    .container,
    .halwasport-container,
    .fawanews-container {
        padding: 16px 12px;
    }

    .match-card {
        padding: 16px;
        margin-bottom: 14px;
    }

    .match-teams {
        gap: 6px;
        padding: 6px 0;
        margin-bottom: 12px;
    }

    .team {
        max-width: 32%;
    }

    .team-logo {
        width: 56px;
        height: 56px;
        margin-bottom: 6px;
    }

    .team-logo img {
        width: 42px;
        height: 42px;
    }

    .team-name {
        font-size: 11px;
        line-height: 1.2;
    }

    .match-center {
        min-width: 100px;
        margin: 0 8px;
    }

    .match-time-center {
        padding: 12px 8px;
        min-height: 56px;
    }

    .center-time {
        font-size: 15px;
    }

    .center-status {
        font-size: 10px;
        padding: 3px 8px;
    }

    .tab-item {
        padding: 10px 12px;
        font-size: 13px;
    }

    .match-info {
        padding-top: 10px;
        margin-bottom: 0;
    }

    .competition {
        font-size: 11px;
    }

    .commentary,
    .tv {
        font-size: 9px;
    }

    .broadcast-info {
        gap: 10px;
    }
}

/* Extra small screens (320px - 479px) */
@media (max-width: 479px) {

    .container,
    .halwasport-container,
    .fawanews-container {
        padding: 12px 8px;
    }

    .match-card {
        padding: 14px;
        margin-bottom: 12px;
    }

    .match-teams {
        gap: 4px;
        padding: 4px 0;
        margin-bottom: 10px;
    }

    .team {
        max-width: 30%;
    }

    .team-logo {
        width: 48px;
        height: 48px;
        margin-bottom: 6px;
    }

    .team-logo img {
        width: 36px;
        height: 36px;
    }

    .team-name {
        font-size: 10px;
        line-height: 1.2;
    }

    .match-center {
        min-width: 88px;
        margin: 0 6px;
    }

    .match-time-center {
        padding: 10px 6px;
        min-height: 48px;
    }

    .center-time {
        font-size: 14px;
    }

    .center-status {
        font-size: 9px;
        padding: 2px 6px;
    }

    .tab-item {
        padding: 8px 8px;
        font-size: 12px;
    }

    .tab-bar {
        padding: 4px;
        margin-bottom: 16px;
    }

    .match-info {
        padding-top: 8px;
        margin-bottom: 0;
    }

    .competition {
        font-size: 10px;
    }

    .commentary,
    .tv {
        font-size: 8px;
    }

    .broadcast-info {
        gap: 8px;
    }
}

/* Additional mobile improvements */
@media (max-width: 767px) {
    .match-card {
        min-height: 120px;
        touch-action: manipulation;
    }

    .match-card:active {
        transform: scale(0.98);
    }

    .team-name {
        word-break: break-word;
        hyphens: auto;
    }

    .match-overlay {
        border-radius: 16px;
    }

    .overlay-text {
        font-size: 14px;
        padding: 8px 12px;
    }
}