<?php
/**
 * Match Data Processor Class
 *
 * @package FawaNews
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class FawaNews_Match_Processor {
    private $targetUrl = 'https://fawanews.co.uk/';
    private $targetSelector = '#content > div > div > div > div.nv-content-wrap.entry-content > div.AlbaSportFixture.asp-font';
    

    
    private $hoverTextMapping = [
        "Soon" => "Comming soon",
        "Live" => "live", 
        "Show" => "Watch The Match",
        "NotStarted" => "Not started",
        "FinMatch" => "Match has finished",
        "Finished" => "Finished",
        "NoMatches" => "There are no matches Today"
    ];
    
    private $clickableTexts = ["live", "Watch The Match"];
    
    public function fetchAndProcessMatches($filter = 'today') {
        // Set maximum execution time to prevent timeouts
        set_time_limit(60);

        // Log server capabilities for debugging
        $this->logServerCapabilities();

        try {
            // Yeh function ab WordPress-compatible fetching method istemal karega
            $html = $this->fetchHtmlContent();

            if (!$html) {
                // Error log mein wazeh message hoga ke fetch kyun fail hua
                return $this->createErrorResponse('Unable to fetch data from source using WordPress HTTP API.');
            }

            $allMatches = $this->parseHtmlContent($html);

            return [
                'success' => true,
                'data' => $this->generateMatchCards($allMatches, $filter),
                'allMatches' => $allMatches
            ];

        } catch (Exception $e) {
            error_log("FawaNews Error in fetchAndProcessMatches: " . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    /**
     * Log server capabilities for debugging live server issues
     */
    private function logServerCapabilities() {
        error_log("FawaNews Debug: Server Capabilities Check");
        error_log("FawaNews Debug: - cURL available: " . (function_exists('curl_init') ? 'Yes' : 'No'));
        error_log("FawaNews Debug: - allow_url_fopen: " . (ini_get('allow_url_fopen') ? 'Yes' : 'No'));
        error_log("FawaNews Debug: - wp_remote_get available: " . (function_exists('wp_remote_get') ? 'Yes' : 'No'));
        error_log("FawaNews Debug: - PHP version: " . PHP_VERSION);
        error_log("FawaNews Debug: - WordPress version: " . get_bloginfo('version'));

        // Check if there are any HTTP restrictions
        $blocked_hosts = get_option('http_request_blocked_hosts', []);
        if (!empty($blocked_hosts)) {
            error_log("FawaNews Debug: - Blocked hosts: " . implode(', ', $blocked_hosts));
        }
    }
    
    /**
     * Fetches HTML content using the reliable WordPress HTTP API.
     * Replaces fetchDirect, fetchWithCurl, and fetchWithProxy.
     */
    private function fetchHtmlContent() {
        error_log("FawaNews Debug: Starting fetchHtmlContent for URL: " . $this->targetUrl);

        $args = [
            'timeout'     => 30, // Increased timeout for live servers
            'user-agent'  => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'sslverify'   => false, // SSL verification ko bypass karein agar zaroori ho
            'headers'     => [
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
                'Accept-Encoding' => 'gzip, deflate',
                'Connection' => 'keep-alive',
                'Upgrade-Insecure-Requests' => '1'
            ]
        ];

        error_log("FawaNews Debug: wp_remote_get args: " . print_r($args, true));

        // Use WordPress's standard function to get external content
        $response = wp_remote_get($this->targetUrl, $args);

        // Check for errors
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $error_code = $response->get_error_code();
            error_log("FawaNews wp_remote_get error - Code: " . $error_code . ", Message: " . $error_message);
            return false;
        }

        // Check for successful response code
        $http_code = wp_remote_retrieve_response_code($response);
        $response_headers = wp_remote_retrieve_headers($response);

        error_log("FawaNews Debug: HTTP response code: " . $http_code);
        error_log("FawaNews Debug: Response headers: " . print_r($response_headers, true));

        if ($http_code !== 200) {
            error_log("FawaNews HTTP error: Received code " . $http_code);
            return false;
        }

        $body = wp_remote_retrieve_body($response);

        // Debug: Log the content length and first 500 characters
        error_log("FawaNews Debug: Fetched content length: " . strlen($body));
        error_log("FawaNews Debug: First 500 chars: " . substr($body, 0, 500));

        // Check if the body is valid
        if (empty($body) || strlen($body) < 500) { // 500 characters se kam data ko invalid samjhein
            error_log("FawaNews error: Fetched content is empty or too short. Length: " . strlen($body));

            // Try fallback method for live servers
            error_log("FawaNews Debug: Trying fallback fetch method...");
            return $this->fetchHtmlContentFallback();
        }

        // Return the HTML content on success
        error_log("FawaNews Debug: Successfully fetched content");
        return $body;
    }

    /**
     * Fallback method for live servers where wp_remote_get might be restricted
     */
    private function fetchHtmlContentFallback() {
        error_log("FawaNews Debug: Using fallback fetch method");

        // Try cURL if available
        if (function_exists('curl_init')) {
            error_log("FawaNews Debug: Trying cURL fallback");

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->targetUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language: en-US,en;q=0.5',
                'Accept-Encoding: gzip, deflate',
                'Connection: keep-alive'
            ]);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            error_log("FawaNews Debug: cURL result - HTTP Code: " . $httpCode . ", Content length: " . strlen($result) . ", Error: " . $curlError);

            if ($httpCode === 200 && $result && strlen($result) > 500) {
                error_log("FawaNews Debug: cURL fallback successful");
                return $result;
            }
        }

        // Try file_get_contents as last resort
        if (ini_get('allow_url_fopen')) {
            error_log("FawaNews Debug: Trying file_get_contents fallback");

            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language: en-US,en;q=0.5'
                    ],
                    'timeout' => 30,
                    'ignore_errors' => true
                ]
            ]);

            $result = @file_get_contents($this->targetUrl, false, $context);

            if ($result && strlen($result) > 500) {
                error_log("FawaNews Debug: file_get_contents fallback successful");
                return $result;
            }
        }

        error_log("FawaNews Error: All fetch methods failed on live server");
        return false;
    }
    



    

    
    private function parseHtmlContent($html) {
        try {
            $dom = new DOMDocument();
            @$dom->loadHTML($html);
            $xpath = new DOMXPath($dom);

            // Debug: Check if we can find any divs with AlbaSportFixture class
            error_log("FawaNews Debug: Looking for AlbaSportFixture elements...");

            // Find the main fixture element
            $fixtureElements = $xpath->query("//div[contains(@class, 'AlbaSportFixture')]");

            error_log("FawaNews Debug: Found " . ($fixtureElements ? $fixtureElements->length : 0) . " AlbaSportFixture elements");

            if ($fixtureElements === false || $fixtureElements->length === 0) {
                // Debug: Let's see what div classes are actually present
                $allDivs = $xpath->query("//div[@class]");
                if ($allDivs && $allDivs->length > 0) {
                    $classes = [];
                    for ($i = 0; $i < min(10, $allDivs->length); $i++) {
                        $classes[] = $allDivs->item($i)->getAttribute('class');
                    }
                    error_log("FawaNews Debug: First 10 div classes found: " . implode(', ', $classes));
                }
                return [];
            }

            $fixtureElement = $fixtureElements->item(0);
            return $this->parseMatchesData($fixtureElement, $xpath);
        } catch (Exception $e) {
            error_log("FawaNews Error parsing HTML content: " . $e->getMessage());
            return [];
        }
    }
    
    private function parseMatchesData($fixtureElement, $xpath) {
        $allMatches = [
            'yesterday' => [],
            'today' => [],
            'tomorrow' => []
        ];
        
        $globalId = 1;
        
        // Parse matches from each day's div
        $days = ['yesterday', 'today', 'tomorrow'];
        
        foreach ($days as $day) {
            $dayDiv = $xpath->query(".//div[@id='aspwp-{$day}']", $fixtureElement);
            
            if ($dayDiv !== false && $dayDiv->length > 0) {
                $matchElements = $xpath->query(".//div[contains(@class, 'AF_Match')]", $dayDiv->item(0));
                
                if ($matchElements !== false) {
                    foreach ($matchElements as $matchEl) {
                        try {
                            $match = $this->createMatchObject($matchEl, $xpath, $globalId++, $day);
                            $allMatches[$day][] = $match;
                        } catch (Exception $e) {
                            error_log("Error creating match object: " . $e->getMessage());
                            continue;
                        }
                    }
                }
            }
        }
        
        return $allMatches;
    }
    
    private function createMatchObject($matchEl, $xpath, $id, $day) {
        return [
            'id' => $id,
            'day' => $day,
            'status' => $this->getMatchStatus($matchEl),
            'startTime' => $matchEl->getAttribute('data-start') ?: '',
            'homeTeam' => $this->extractTeamData($matchEl, $xpath, '.AF_FTeam'),
            'awayTeam' => $this->extractTeamData($matchEl, $xpath, '.AF_STeam'),
            'time' => $this->extractMatchTime($matchEl, $xpath),
            'timezone' => $this->extractTimezone($matchEl, $xpath),
            'competition' => $this->extractCompetition($matchEl, $xpath),
            'broadcastInfo' => $this->extractBroadcastInfo($matchEl, $xpath),
            'matchUrl' => $this->extractMatchUrl($matchEl, $xpath),
            'hoverText' => $this->extractHoverText($matchEl, $xpath)
        ];
    }
    
    private function getMatchStatus($matchEl) {
        $classes = $matchEl->getAttribute('class');
        if (strpos($classes, 'finished') !== false) return 'finished';
        if (strpos($classes, 'live') !== false) return 'live';
        if (strpos($classes, 'comming-soon') !== false) return 'coming-soon';
        if (strpos($classes, 'not-started') !== false) return 'not-started';
        return 'unknown';
    }
    
    private function extractTeamData($matchEl, $xpath, $selector) {
        // Fix XPath selector - remove the dot from CSS class selector
        $classSelector = str_replace('.', '', $selector);
        $teamElements = $xpath->query(".//div[contains(@class, '{$classSelector}')]", $matchEl);
        
        if ($teamElements === false || $teamElements->length === 0) {
            return ['name' => 'Unknown', 'logo' => ''];
        }
        
        $teamEl = $teamElements->item(0);
        $nameElements = $xpath->query(".//div[contains(@class, 'AF_TeamName')]", $teamEl);
        $logoElements = $xpath->query(".//div[contains(@class, 'AF_TeamLogo')]//img", $teamEl);
        
        return [
            'name' => ($nameElements !== false && $nameElements->length > 0) ? trim($nameElements->item(0)->textContent) : 'Unknown',
            'logo' => ($logoElements !== false && $logoElements->length > 0) ? $logoElements->item(0)->getAttribute('src') : ''
        ];
    }
    
    private function extractMatchTime($matchEl, $xpath) {
        $timeElements = $xpath->query(".//div[contains(@class, 'AF_EvTime')]", $matchEl);
        $statusElements = $xpath->query(".//div[contains(@class, 'AF_StaText')]", $matchEl);
        
        return [
            'scheduled' => ($timeElements !== false && $timeElements->length > 0) ? trim($timeElements->item(0)->textContent) : '',
            'status' => ($statusElements !== false && $statusElements->length > 0) ? trim($statusElements->item(0)->textContent) : ''
        ];
    }
    
    private function extractTimezone($matchEl, $xpath) {
        // Look for timezone in the match data section
        $timezoneElements = $xpath->query(".//div[@class='asp-city']", $matchEl);
        if ($timezoneElements !== false && $timezoneElements->length > 0) {
            return trim($timezoneElements->item(0)->textContent);
        }

        // Fallback to contains class search
        $timezoneElements = $xpath->query(".//div[contains(@class, 'asp-city')]", $matchEl);
        return ($timezoneElements !== false && $timezoneElements->length > 0) ? trim($timezoneElements->item(0)->textContent) : 'Unknown';
    }
    
    private function extractCompetition($matchEl, $xpath) {
        // Look for competition in the AF_Footer section specifically
        $searches = [
            ".//div[@class='AF_Footer']//span[@class='cup asp-flex']",
            ".//div[contains(@class, 'AF_Footer')]//span[contains(@class, 'cup')]",
            ".//div[contains(@class, 'AF_EvInfo')]//span[contains(@class, 'cup')]",
            ".//span[@class='cup asp-flex']",
            ".//span[contains(@class, 'cup')]"
        ];

        foreach ($searches as $query) {
            $elements = $xpath->query($query, $matchEl);
            if ($elements !== false && $elements->length > 0) {
                $text = trim($elements->item(0)->textContent);
                if (!empty($text) && $text !== 'Unknown') {
                    return $text;
                }
            }
        }

        return 'Unknown';
    }
    
    private function extractBroadcastInfo($matchEl, $xpath) {
        // Look for broadcast info in the AF_Footer section specifically
        $micElements = $xpath->query(".//div[contains(@class, 'AF_Footer')]//span[contains(@class, 'mic')]", $matchEl);
        $tvElements = $xpath->query(".//div[contains(@class, 'AF_Footer')]//span[contains(@class, 'tv')]", $matchEl);

        // Fallback to any mic/tv elements
        if ($micElements === false || $micElements->length === 0) {
            $micElements = $xpath->query(".//span[contains(@class, 'mic')]", $matchEl);
        }
        if ($tvElements === false || $tvElements->length === 0) {
            $tvElements = $xpath->query(".//span[contains(@class, 'tv')]", $matchEl);
        }

        return [
            'commentary' => ($micElements !== false && $micElements->length > 0) ? trim($micElements->item(0)->textContent) : 'Unknown',
            'tv' => ($tvElements !== false && $tvElements->length > 0) ? trim($tvElements->item(0)->textContent) : 'Unknown'
        ];
    }
    
    private function extractMatchUrl($matchEl, $xpath) {
        $linkElements = $xpath->query(".//a[contains(@class, 'AF_EventMask')]", $matchEl);
        return ($linkElements !== false && $linkElements->length > 0) ? $linkElements->item(0)->getAttribute('href') : '';
    }
    
    private function extractHoverText($matchEl, $xpath) {
        $hoverElements = $xpath->query(".//div[contains(@class, 'AF_MaskText')]", $matchEl);
        return ($hoverElements !== false && $hoverElements->length > 0) ? trim($hoverElements->item(0)->textContent) : '';
    }
    
    private function filterMatchesByDay($allMatches, $filter) {
        switch ($filter) {
            case 'yesterday':
                return $allMatches['yesterday'] ?? [];
            case 'today':
                return $allMatches['today'] ?? [];
            case 'tomorrow':
                return $allMatches['tomorrow'] ?? [];
            default:
                return $allMatches['today'] ?? [];
        }
    }

    private function generateMatchCards($allMatches, $filter) {
        // Get matches for the specific filter
        $filteredMatches = $this->filterMatchesByDay($allMatches, $filter);

        if (empty($filteredMatches)) {
            return $this->createEmptyStateMessage($filter);
        }

        $sortedMatches = $this->sortMatchesByPriority($filteredMatches);

        $html = '<div class="matches-container">';

        foreach ($sortedMatches as $match) {
            $html .= $this->createMatchCard($match);
        }

        $html .= '</div>';

        return $html;
    }

    private function sortMatchesByPriority($matches) {
        usort($matches, function($a, $b) {
            $priorityA = $this->getMatchPriority($a['hoverText']);
            $priorityB = $this->getMatchPriority($b['hoverText']);

            if ($priorityA !== $priorityB) {
                return $priorityA - $priorityB;
            }

            return strcmp($a['time']['scheduled'], $b['time']['scheduled']);
        });

        return $matches;
    }

    private function getMatchPriority($hoverText) {
        $text = strtolower($hoverText);

        if (strpos($text, 'live') !== false || $text === 'watch the match') {
            return 1; // Highest priority - Live matches
        } elseif (strpos($text, 'comming soon') !== false) {
            return 2; // Coming soon matches
        } elseif (strpos($text, 'not started') !== false) {
            return 3; // Not started matches
        } elseif (strpos($text, 'finished') !== false || $text === 'match has finished') {
            return 4; // Lowest priority - Finished matches
        }

        return 4; // Default to lowest priority
    }

    private function createMatchCard($match) {
        $statusClass = "status-{$match['status']}";
        $overlayText = $this->getDisplayHoverText($match['hoverText']);
        $centerContent = $this->getCenterContent($match);
        $isClickable = $this->isMatchClickable($match['hoverText']);
        $cursorStyle = $isClickable ? 'cursor: pointer;' : 'cursor: default;';

        $overlayHtml = '';
        if ($isClickable) {
            $overlayHtml = "
            <div class=\"match-overlay\">
                <div class=\"overlay-text\">{$overlayText}</div>
            </div>";
        }

        $clickHandler = $isClickable ? "onclick=\"fawanewsHandleMatchClick('{$match['matchUrl']}', {$match['id']})\"" : '';

        return "
        <div class=\"match-card {$statusClass}\" data-match-id=\"{$match['id']}\"
             {$clickHandler} style=\"{$cursorStyle}\">
            {$overlayHtml}

            <div class=\"match-teams\">
                <div class=\"team home-team\">
                    <div class=\"team-logo\">
                        <img src=\"{$match['homeTeam']['logo']}\" alt=\"{$match['homeTeam']['name']}\" onerror=\"this.style.display='none'\">
                    </div>
                    <div class=\"team-name\">{$match['homeTeam']['name']}</div>
                </div>

                <div class=\"match-center\">
                    <div class=\"match-time-center\">
                        <div class=\"center-time\">{$centerContent['time']}</div>
                        <div class=\"center-status {$centerContent['statusClass']}\">{$centerContent['statusText']}</div>
                    </div>
                </div>

                <div class=\"team away-team\">
                    <div class=\"team-logo\">
                        <img src=\"{$match['awayTeam']['logo']}\" alt=\"{$match['awayTeam']['name']}\" onerror=\"this.style.display='none'\">
                    </div>
                    <div class=\"team-name\">{$match['awayTeam']['name']}</div>
                </div>
            </div>

            <div class=\"match-info\">
                <div class=\"competition\">
                    <i class=\"icon-trophy\"></i>
                    {$match['competition']}
                </div>
            </div>
        </div>";
    }

    private function getCenterContent($match) {
        $hoverText = strtolower($match['hoverText']);
        $statusClass = '';
        $statusText = '';

        if (strpos($hoverText, 'live') !== false || $hoverText === 'watch the match') {
            $statusClass = 'live';
            $statusText = 'Live';
        } elseif (strpos($hoverText, 'finished') !== false || $hoverText === 'match has finished') {
            $statusClass = 'finished';
            $statusText = 'Match Finished';
        } elseif (strpos($hoverText, 'not started') !== false || strpos($hoverText, 'comming soon') !== false) {
            $statusClass = 'upcoming';
            $statusText = 'Upcoming';
        } else {
            $statusClass = 'upcoming';
            $statusText = 'Upcoming';
        }

        return [
            'time' => $match['time']['scheduled'] ?: 'TBD',
            'statusClass' => $statusClass,
            'statusText' => $statusText
        ];
    }

    private function isMatchClickable($hoverText) {
        // Make all cards clickable if they have hover text
        return !empty($hoverText);
    }

    private function getDisplayHoverText($hoverText) {
        return $hoverText ?: 'View Match';
    }

    private function createEmptyStateMessage($filter) {
        $messages = [
            'today' => [
                'icon' => '📅',
                'title' => 'No Events Today',
                'description' => 'There are no events scheduled for today. Check other days for upcoming events!'
            ],
            'yesterday' => [
                'icon' => '⏮️',
                'title' => 'No Events Yesterday',
                'description' => 'There were no events yesterday. Check today\'s or tomorrow\'s schedule!'
            ],
            'tomorrow' => [
                'icon' => '⏭️',
                'title' => 'No Events Tomorrow',
                'description' => 'There are no events scheduled for tomorrow yet. Check back later for updates!'
            ]
        ];

        $message = $messages[$filter] ?? $messages['today'];

        return "
        <div class=\"no-matches\">
            <div class=\"no-matches-icon\">{$message['icon']}</div>
            <div class=\"no-matches-title\">{$message['title']}</div>
            <div class=\"no-matches-description\">{$message['description']}</div>
        </div>";
    }

    private function createErrorResponse($message) {
        return [
            'success' => false,
            'error' => $message,
            'data' => "
            <div class=\"no-matches\">
                <div class=\"no-matches-icon\">⚠️</div>
                <div class=\"no-matches-title\">Unable to Load Matches</div>
                <div class=\"no-matches-description\">There was an issue loading the match data. Please try refreshing the page or check back later.</div>
            </div>"
        ];
    }
}
