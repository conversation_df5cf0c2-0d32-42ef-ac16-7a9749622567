<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Football Matches v1.1</title>
    <style>
        /* Base Styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: transparent;
            min-height: 100vh;
            color: #2d3748;
            line-height: 1.6;
        }

        .container,
        .mubashardev-container,
        .halwasport-container,
        .fawanews-container {
            padding: 24px 16px;
            max-width: 1200px;
            margin: 0 auto;
            background: transparent;
        }

        /* Loading Styles */
        .loading-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        /* Tab Bar Styles */
        .tab-bar {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 6px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            position: relative;
            color: #64748b;
        }

        .tab-item:hover {
            background: rgba(255, 99, 68, 0.1);
            color: #FF6344;
            transform: translateY(-1px);
        }

        .tab-item.active {
            background: linear-gradient(135deg, #FF6344 0%, #e55039 100%);
            color: white;
            box-shadow: 0 4px 20px rgba(255, 99, 68, 0.4);
            transform: translateY(-1px);
        }

        .tab-item.active:hover {
            background: linear-gradient(135deg, #e55039 0%, #c44569 100%);
            color: white;
        }

        /* Loading Spinner */
        .loader-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #FF6344;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Data Container */
        .data-container {
            width: 100%;
        }

        .data-content {
            width: 100%;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #64748b;
        }

        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 12px;
            color: #374151;
        }

        .empty-state p {
            font-size: 16px;
            line-height: 1.6;
            max-width: 400px;
            margin: 0 auto;
        }

        /* Error State */
        .error-state {
            text-align: center;
            padding: 60px 20px;
            color: #ef4444;
        }

        .error-state h3 {
            font-size: 24px;
            margin-bottom: 12px;
            color: #dc2626;
        }

        .error-state p {
            font-size: 16px;
            line-height: 1.6;
            max-width: 400px;
            margin: 0 auto;
            color: #64748b;
        }

        /* Match Cards Styles */
        .matches-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
            padding: 0;
            margin: 0;
        }

        .match-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .match-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
            transition: all 0.4s ease;
            border-radius: 20px 20px 0 0;
        }

        .match-card[style*="cursor: pointer"]:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 99, 68, 0.3);
        }

        .match-card[style*="cursor: pointer"]:hover::before {
            background: linear-gradient(90deg, #FF6344, #e55039);
            height: 4px;
        }

        .match-card[style*="cursor: pointer"]:hover .match-overlay {
            opacity: 1;
            visibility: visible;
        }

        /* Status-based card styling */
        .match-card.status-live::before {
            background: linear-gradient(90deg, #ef4444, #dc2626) !important;
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
        }

        .match-card.status-finished::before {
            background: linear-gradient(90deg, #64748b, #475569) !important;
        }

        .match-card.status-coming-soon::before {
            background: linear-gradient(90deg, #f59e0b, #d97706) !important;
        }

        .match-card.status-not-started::before {
            background: linear-gradient(90deg, #10b981, #059669) !important;
        }

        /* Match Overlay */
        .match-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.28);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 20px;
            z-index: 10;
        }

        .overlay-text {
            color: white;
            font-size: 16px;
            font-weight: 700;
            text-align: center;
            padding: 12px 16px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        }

        .status-live .match-overlay {
            background: rgba(0, 0, 0, 0.28);
        }

        .status-finished .match-overlay {
            background: rgba(0, 0, 0, 0.28);
        }

        .status-coming-soon .match-overlay {
            background: rgba(0, 0, 0, 0.28);
        }

        .status-not-started .match-overlay {
            background: rgba(0, 0, 0, 0.28);
        }

        /* Match Teams */
        .match-teams {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
            padding: 8px 0;
        }

        .team {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            max-width: 35%;
            position: relative;
        }

        .team-logo {
            width: 64px;
            height: 64px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid rgba(255, 99, 68, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        .match-card:hover .team-logo {
            transform: scale(1.08) rotate(2deg);
            border-color: rgba(255, 99, 68, 0.3);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .team-logo img {
            width: 48px;
            height: 48px;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .team-name {
            font-weight: 600;
            font-size: 13px;
            color: #374151;
            text-align: center;
            margin-bottom: 4px;
            line-height: 1.3;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .team-score {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            margin-top: 4px;
            text-align: center;
        }

        /* Match Center Status */
        .match-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex: 0 0 auto;
            margin: 0 16px;
            min-width: 120px;
        }

        .match-time-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 16px 12px;
            border-radius: 16px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid rgba(255, 99, 68, 0.1);
            min-height: 64px;
            justify-content: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .match-card:hover .match-time-center {
            border-color: rgba(255, 99, 68, 0.2);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .center-time {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            text-align: center;
            line-height: 1.2;
            letter-spacing: -0.5px;
        }

        .center-status {
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            text-align: center;
            padding: 4px 10px;
            border-radius: 20px;
        }

        .center-status.live {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
        }

        .center-status.upcoming {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
        }

        .center-status.finished {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
            box-shadow: 0 4px 16px rgba(100, 116, 139, 0.3);
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
                transform: scale(1);
            }

            50% {
                opacity: 0.8;
                transform: scale(1.05);
            }
        }

        /* Match Info */
        .match-info {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 12px;
            border-top: 1px solid rgba(226, 232, 240, 0.8);
            margin-bottom: 0;
        }

        .competition {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 600;
            color: #64748b;
            font-size: 12px;
            text-align: center;
            line-height: 1.4;
        }

        .competition-logo {
            width: 20px;
            height: 20px;
            object-fit: contain;
            border-radius: 4px;
        }



        /* Try Again Message */
        .try-again-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(239, 68, 68, 0.95);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            z-index: 20;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        /* Large screens (1200px+) */
        @media (min-width: 1200px) {
            .match-card {
                padding: 24px;
            }

            .team-logo {
                width: 72px;
                height: 72px;
            }

            .team-logo img {
                width: 56px;
                height: 56px;
            }

            .center-time {
                font-size: 20px;
            }

            .team-name {
                font-size: 14px;
            }

            .competition {
                font-size: 13px;
            }
        }

        /* Medium screens (768px - 1199px) */
        @media (min-width: 768px) and (max-width: 1199px) {

            .container,
            .halwasport-container,
            .fawanews-container {
                padding: 20px 12px;
            }

            .match-card {
                padding: 18px;
            }

            .team-logo {
                width: 60px;
                height: 60px;
            }

            .team-logo img {
                width: 44px;
                height: 44px;
            }

            .match-center {
                min-width: 110px;
                margin: 0 12px;
            }

            .match-time-center {
                padding: 14px 10px;
                min-height: 60px;
            }

            .center-time {
                font-size: 16px;
            }

            .team-name {
                font-size: 12px;
            }

            .competition {
                font-size: 11px;
            }


        }

        /* Small screens (480px - 767px) */
        @media (min-width: 480px) and (max-width: 767px) {

            .container,
            .halwasport-container,
            .fawanews-container {
                padding: 16px 12px;
            }

            .match-card {
                padding: 16px;
                margin-bottom: 14px;
            }

            .match-teams {
                gap: 6px;
                padding: 6px 0;
                margin-bottom: 12px;
            }

            .team {
                max-width: 32%;
            }

            .team-logo {
                width: 56px;
                height: 56px;
                margin-bottom: 6px;
            }

            .team-logo img {
                width: 42px;
                height: 42px;
            }

            .team-name {
                font-size: 11px;
                line-height: 1.2;
            }

            .match-center {
                min-width: 100px;
                margin: 0 8px;
            }

            .match-time-center {
                padding: 12px 8px;
                min-height: 56px;
            }

            .center-time {
                font-size: 15px;
            }

            .center-status {
                font-size: 10px;
                padding: 3px 8px;
            }

            .tab-item {
                padding: 10px 12px;
                font-size: 13px;
            }

            .match-info {
                padding-top: 10px;
                margin-bottom: 0;
            }

            .competition {
                font-size: 11px;
            }


        }

        /* Extra small screens (320px - 479px) */
        @media (max-width: 479px) {

            .container,
            .halwasport-container,
            .fawanews-container {
                padding: 12px 8px;
            }

            .match-card {
                padding: 14px;
                margin-bottom: 12px;
            }

            .match-teams {
                gap: 4px;
                padding: 4px 0;
                margin-bottom: 10px;
            }

            .team {
                max-width: 30%;
            }

            .team-logo {
                width: 48px;
                height: 48px;
                margin-bottom: 6px;
            }

            .team-logo img {
                width: 36px;
                height: 36px;
            }

            .team-name {
                font-size: 10px;
                line-height: 1.2;
            }

            .match-center {
                min-width: 88px;
                margin: 0 6px;
            }

            .match-time-center {
                padding: 10px 6px;
                min-height: 48px;
            }

            .center-time {
                font-size: 14px;
            }

            .center-status {
                font-size: 9px;
                padding: 2px 6px;
            }

            .tab-item {
                padding: 8px 8px;
                font-size: 12px;
            }

            .tab-bar {
                padding: 4px;
                margin-bottom: 16px;
            }

            .match-info {
                padding-top: 8px;
                margin-bottom: 0;
            }

            .competition {
                font-size: 10px;
            }


        }
    </style>
</head>

<body>
    <div class="container">
        <div class="loading-info" id="loadingInfo">
            <div class="loader-spinner"></div>
            <p>Loading matches...</p>
        </div>

        <div class="tab-bar" id="tabBar" style="display: none;">
            <div class="tab-item" data-filter="yesterday">
                <span class="tab-text">Yesterday</span>
            </div>
            <div class="tab-item active" data-filter="today">
                <span class="tab-text">Today</span>
            </div>
            <div class="tab-item" data-filter="tomorrow">
                <span class="tab-text">Tomorrow</span>
            </div>
        </div>

        <div id="status"></div>
        <div id="dataContainer" class="data-container" style="display: none;">
            <div id="dataContent" class="data-content"></div>
        </div>
    </div>
    <script>
        // DOM Elements
        const loadingInfo = document.getElementById('loadingInfo');
        const statusDiv = document.getElementById('status');
        const dataContainer = document.getElementById('dataContainer');
        const dataContent = document.getElementById('dataContent');
        const tabBar = document.getElementById('tabBar');

        // Backend endpoint
        const BACKEND_URL = 'backend.php';

        // Current filter state and data storage
        let currentFilter = 'today';
        let allMatchesData = null;

        // Utility Functions
        function showLoading() {
            loadingInfo.style.display = 'flex';
            hideData();
            hideTabBar();
        }

        function hideLoading() {
            loadingInfo.style.display = 'none';
        }

        function showData(data) {
            dataContent.innerHTML = data;
            dataContainer.style.display = 'block';
            showTabBar();
            setTimeout(startCountdownTimers, 100);
        }

        function hideData() {
            dataContainer.style.display = 'none';
        }

        function showTabBar() {
            tabBar.style.display = 'flex';
        }

        function hideTabBar() {
            tabBar.style.display = 'none';
        }

        // Data Fetching Functions
        async function fetchData(filter = 'today') {
            try {
                showLoading();
                currentFilter = filter;

                // Only fetch from server if we don't have data yet
                if (!allMatchesData) {
                    const response = await fetch(`${BACKEND_URL}?filter=${encodeURIComponent(filter)}`, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }

                    const result = await response.json();

                    if (result.success) {
                        // Store all matches data for client-side filtering
                        allMatchesData = result.allMatches;
                        showData(result.data);
                    } else {
                        showData(result.data); // Error message from backend
                    }
                } else {
                    // Use cached data and filter client-side
                    const filteredData = filterAndGenerateCards(filter);
                    showData(filteredData);
                }

                hideLoading();

            } catch (error) {
                hideLoading();
                showData(createErrorMessage());
            }
        }

        function createErrorMessage() {
            return `
    <div class="no-matches">
        <div class="no-matches-icon">📋</div>
        <div class="no-matches-title">No Events Available</div>
        <div class="no-matches-description">There are no events scheduled at the moment. Please check back later.</div>
    </div>`;
        }

        // Client-side filtering function
        function filterAndGenerateCards(filter) {
            if (!allMatchesData) {
                return createErrorMessage();
            }

            // Get matches for the specific filter
            let filteredMatches = [];
            switch (filter) {
                case 'yesterday':
                    filteredMatches = allMatchesData.yesterday || [];
                    break;
                case 'today':
                    filteredMatches = allMatchesData.today || [];
                    break;
                case 'tomorrow':
                    filteredMatches = allMatchesData.tomorrow || [];
                    break;
                default:
                    filteredMatches = allMatchesData.today || [];
            }

            if (filteredMatches.length === 0) {
                return createEmptyStateMessage(filter);
            }

            // Sort matches by priority (live first)
            const sortedMatches = sortMatchesByPriority(filteredMatches);

            // Generate HTML
            let html = '<div class="matches-container">';

            sortedMatches.forEach(match => {
                html += createMatchCard(match);
            });

            html += '</div>';
            return html;
        }

        function createEmptyStateMessage(filter) {
            const messages = {
                today: {
                    icon: '📅',
                    title: 'No Events Today',
                    description: 'There are no events scheduled for today. Check other days for upcoming events!'
                },
                yesterday: {
                    icon: '⏮️',
                    title: 'No Events Yesterday',
                    description: 'There were no events yesterday. Check today\'s or tomorrow\'s schedule!'
                },
                tomorrow: {
                    icon: '⏭️',
                    title: 'No Events Tomorrow',
                    description: 'There are no events scheduled for tomorrow yet. Check back later for updates!'
                }
            };

            const message = messages[filter] || messages.today;

            return `
    <div class="no-matches">
        <div class="no-matches-icon">${message.icon}</div>
        <div class="no-matches-title">${message.title}</div>
        <div class="no-matches-description">${message.description}</div>
    </div>`;
        }

        // Helper functions for client-side processing
        function sortMatchesByPriority(matches) {
            return matches.sort((a, b) => {
                const priorityA = getMatchPriority(a.hoverText);
                const priorityB = getMatchPriority(b.hoverText);

                if (priorityA !== priorityB) {
                    return priorityA - priorityB;
                }

                if (a.time.scheduled && b.time.scheduled) {
                    return a.time.scheduled.localeCompare(b.time.scheduled);
                }

                return 0;
            });
        }

        function getMatchPriority(hoverText) {
            if (!hoverText) return 4;

            const text = hoverText.toLowerCase();

            if (text.includes('live') || text === 'watch the match') {
                return 1; // Highest priority - Live matches
            } else if (text.includes('comming soon')) {
                return 2; // Coming soon matches
            } else if (text.includes('not started')) {
                return 3; // Not started matches
            } else if (text.includes('finished') || text === 'match has finished') {
                return 4; // Lowest priority - Finished matches
            }

            return 4; // Default to lowest priority
        }

        function createMatchCard(match) {
            const statusClass = `status-${match.status}`;
            const overlayText = match.hoverText || 'View Match';
            const centerContent = getCenterContent(match);
            const isClickable = isMatchClickable(match.hoverText, match.matchUrl);
            const cursorStyle = isClickable ? 'cursor: pointer;' : 'cursor: default; opacity: 0.7;';

            const overlayHtml = isClickable ? `
        <div class="match-overlay">
            <div class="overlay-text">${overlayText}</div>
        </div>` : '';

            const clickHandler = isClickable ? `onclick="handleMatchClick('${match.matchUrl}', ${match.id})"` : '';

            return `
    <div class="match-card ${statusClass}" data-match-id="${match.id}"
         ${clickHandler} style="${cursorStyle}">
        ${overlayHtml}

        <div class="match-teams">
            <div class="team home-team">
                <div class="team-logo">
                    <img src="${match.homeTeam.logo}" alt="${match.homeTeam.name}" onerror="this.style.display='none'">
                </div>
                <div class="team-name">${match.homeTeam.name}</div>
            </div>

            <div class="match-center">
                <div class="match-time-center">
                    <div class="center-time">${centerContent.time}</div>
                    <div class="center-status ${centerContent.statusClass}">${centerContent.statusText}</div>
                </div>
            </div>

            <div class="team away-team">
                <div class="team-logo">
                    <img src="${match.awayTeam.logo}" alt="${match.awayTeam.name}" onerror="this.style.display='none'">
                </div>
                <div class="team-name">${match.awayTeam.name}</div>
            </div>
        </div>

        <div class="match-info">
            <div class="competition">
                ${match.competition}
            </div>
        </div>
    </div>`;
        }

        function getCenterContent(match) {
            const hoverText = match.hoverText.toLowerCase();
            let statusClass = '';
            let statusText = '';

            if (hoverText.includes('live') || hoverText === 'watch the match') {
                statusClass = 'live';
                statusText = 'Live';
            } else if (hoverText.includes('finished') || hoverText === 'match has finished') {
                statusClass = 'finished';
                statusText = 'Match Finished';
            } else if (hoverText.includes('not started') || hoverText.includes('comming soon')) {
                statusClass = 'upcoming';
                statusText = 'Upcoming';
            } else {
                statusClass = 'upcoming';
                statusText = 'Upcoming';
            }

            return {
                time: match.time.scheduled || 'TBD',
                statusClass: statusClass,
                statusText: statusText
            };
        }

        function isMatchClickable(hoverText, matchUrl) {
            // Make all cards clickable if they have a URL, regardless of hover text
            return !!(matchUrl && matchUrl.trim());
        }

        // Tab switching functionality
        function switchTab(filter) {
            // Update active tab
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            // Use cached data for filtering (no network call)
            currentFilter = filter;
            if (allMatchesData) {
                const filteredData = filterAndGenerateCards(filter);
                showData(filteredData);
            } else {
                // If no cached data, fetch from server
                fetchData(filter);
            }
        }

        // Match click handler for iframe extraction
        async function handleMatchClick(matchUrl, matchId) {
            if (!matchUrl) return;

            const card = document.querySelector(`[data-match-id="${matchId}"]`);
            if (!card) return;

            // Show loading overlay
            showMatchLoading(card);

            try {
                // Try to extract iframe URL from the match page
                const iframeUrl = await extractIframeUrl(matchUrl);

                if (iframeUrl) {
                    // Open the iframe URL in a new tab
                    window.open(iframeUrl, '_blank');
                    hideMatchLoading(card);
                } else {
                    // Show try again later message
                    showTryAgainMessage(card);
                }
            } catch (error) {
                showTryAgainMessage(card);
            }
        }

        // Helper functions for match loading states
        function showMatchLoading(card) {
            const overlay = document.createElement('div');
            overlay.className = 'click-loading-overlay';
            overlay.innerHTML = `
        <div class="click-loading-content">
            <div class="click-loader"></div>
            <div class="click-loading-text">Checking stream...</div>
        </div>
    `;
            card.appendChild(overlay);
        }

        function hideMatchLoading(card) {
            const overlay = card.querySelector('.click-loading-overlay');
            if (overlay) {
                setTimeout(() => overlay.remove(), 2000);
            }
        }

        function showTryAgainMessage(card) {
            const overlay = card.querySelector('.click-loading-overlay');
            if (overlay) {
                overlay.innerHTML = `
            <div class="click-loading-content try-again">
                <div class="try-again-icon">⏰</div>
                <div class="try-again-title">Try Again Later</div>
                <div class="try-again-text">Stream not available right now</div>
            </div>
        `;
                setTimeout(() => overlay.remove(), 3000);
            }
        }

        // Iframe extraction for clickable matches
        async function extractIframeUrl(matchUrl) {
            try {
                // Make request to backend to extract iframe URL
                const response = await fetch(`iframe-extractor.php?url=${encodeURIComponent(matchUrl)}`);

                if (response.ok) {
                    const result = await response.json();
                    return result.iframeUrl || null;
                }
            } catch (error) {
                // Silent error handling
            }

            return null;
        }

        // Simplified countdown timer (if needed)
        function startCountdownTimers() {
            // Add visual effects to live indicators
            const liveIndicators = document.querySelectorAll('.center-status.live');
            liveIndicators.forEach(indicator => {
                indicator.style.animation = 'pulse 1.5s infinite';
            });
        }

        // Tab click event listeners
        function initializeTabs() {
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.addEventListener('click', () => {
                    const filter = tab.getAttribute('data-filter');
                    switchTab(filter);
                });
            });
        }

        // Initialize
        window.addEventListener('load', () => {
            fetchData();
            initializeTabs();
        });
    </script>
</body>

</html>