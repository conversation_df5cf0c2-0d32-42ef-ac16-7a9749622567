<?php
/**
 * Plugin Name: FawaNews Sports
 * Plugin URI: https://fawanews.com
 * Description: Display live sports matches with professional cards and real-time updates. Use shortcode [MATCH_SCORES] to display matches anywhere.
 * Version: 1.1.3
 * Author: FawaNews
 * Author URI: https://fawanews.com
 * License: GPL v2 or later
 * Text Domain: fawanews
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('FAWANEWS_VERSION', '1.1.3');
define('FAWANEWS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('FAWANEWS_PLUGIN_PATH', plugin_dir_path(__FILE__));

class FawaNews_Plugin {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        // Add proper AJAX script enqueuing for WordPress
        add_action('wp_enqueue_scripts', array($this, 'enqueue_ajax_script'));
        add_shortcode('MATCH_SCORES', array($this, 'match_scores_shortcode'));
        add_action('wp_ajax_fawanews_get_matches', array($this, 'ajax_get_matches'));
        add_action('wp_ajax_nopriv_fawanews_get_matches', array($this, 'ajax_get_matches'));
        add_action('wp_ajax_fawanews_extract_iframe', array($this, 'ajax_extract_iframe'));
        add_action('wp_ajax_nopriv_fawanews_extract_iframe', array($this, 'ajax_extract_iframe'));
        // Debug endpoint
        add_action('wp_ajax_fawanews_debug', array($this, 'ajax_debug'));
        add_action('wp_ajax_nopriv_fawanews_debug', array($this, 'ajax_debug'));
    }
    
    public function init() {
        // Plugin initialization
        load_plugin_textdomain('fawanews', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    public function enqueue_ajax_script() {
        // Only enqueue when shortcode is used - we'll handle this in the shortcode itself
        // This method exists for WordPress hook compatibility
    }
    

    
    public function match_scores_shortcode($atts) {
        $atts = shortcode_atts(array(
            'default_tab' => 'today'
        ), $atts, 'MATCH_SCORES');

        // All CSS and JS are now inlined - no external files needed

        ob_start();
        ?>
        <style>
        /* Complete Professional CSS - Inline with #FF6344 Primary Color */
        .container, .mubashardev-container, .halwasport-container, .fawanews-container {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            padding: 24px 16px;
            max-width: 1200px;
            margin: 0 auto;
            background: transparent;
            color: #2d3748;
            line-height: 1.6;
        }

        .loading-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        .loader-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #FF6344;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-info p {
            font-size: 18px;
            color: #64748b;
            margin: 0;
            font-weight: 500;
        }

        .tab-bar {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 6px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .tab-item {
            flex: 1;
            text-align: center;
            padding: 12px 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            position: relative;
            color: #64748b;
            background: transparent;
            border: none;
            outline: none;
        }

        .tab-item:hover {
            background: rgba(255, 99, 68, 0.1);
            color: #FF6344;
            transform: translateY(-1px);
        }

        .tab-item.active {
            background: linear-gradient(135deg, #FF6344 0%, #e55039 100%);
            color: white;
            box-shadow: 0 4px 20px rgba(255, 99, 68, 0.4);
            transform: translateY(-1px);
        }

        .tab-item.active:hover {
            background: linear-gradient(135deg, #e55039 0%, #c44569 100%);
            color: white;
        }

        .matches-container {
            display: flex;
            flex-direction: column;
            gap: 16px;
            padding: 0;
            margin: 0;
        }

        .match-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .match-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #e2e8f0, #cbd5e1);
            transition: all 0.4s ease;
            border-radius: 20px 20px 0 0;
        }

        .match-card[style*="cursor: pointer"]:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 99, 68, 0.3);
        }

        .match-card[style*="cursor: pointer"]:hover::before {
            background: linear-gradient(90deg, #FF6344, #e55039);
            height: 4px;
        }

        .match-card[style*="cursor: pointer"]:hover .match-overlay {
            opacity: 1;
            visibility: visible;
        }

        /* Status-based card styling */
        .match-card.status-live::before {
            background: linear-gradient(90deg, #ef4444, #dc2626) !important;
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);
        }

        .match-card.status-finished::before {
            background: linear-gradient(90deg, #64748b, #475569) !important;
        }

        .match-card.status-coming-soon::before {
            background: linear-gradient(90deg, #f59e0b, #d97706) !important;
        }

        .match-card.status-not-started::before {
            background: linear-gradient(90deg, #10b981, #059669) !important;
        }

        .match-teams {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 16px;
        }

        .team {
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 35%;
            flex: 1;
        }

        .team-logo {
            width: 64px;
            height: 64px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid rgba(255, 99, 68, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        .match-card:hover .team-logo {
            transform: scale(1.08) rotate(2deg);
            border-color: rgba(255, 99, 68, 0.3);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }

        .team-logo img {
            width: 48px;
            height: 48px;
            object-fit: contain;
            transition: transform 0.3s ease;
        }

        .team-name {
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            color: #374151;
            line-height: 1.3;
            margin: 0;
        }

        .match-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
            margin: 0 16px;
        }

        .center-time {
            font-size: 20px;
            font-weight: bold;
            color: #111827;
            text-align: center;
            line-height: 1.2;
        }

        .match-time-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 16px 12px;
            border-radius: 16px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid rgba(255, 99, 68, 0.1);
            min-height: 64px;
            justify-content: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .match-card:hover .match-time-center {
            border-color: rgba(255, 99, 68, 0.2);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }

        .center-status {
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            text-align: center;
            padding: 4px 10px;
            border-radius: 20px;
        }

        .center-status.live {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
        }

        .center-status.upcoming {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
        }

        .center-status.finished {
            background: linear-gradient(135deg, #64748b, #475569);
            color: white;
            box-shadow: 0 4px 16px rgba(100, 116, 139, 0.3);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .match-info {
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 12px;
            border-top: 1px solid rgba(226, 232, 240, 0.8);
            margin-bottom: 0;
        }

        .competition {
            display: flex;
            align-items: center;
            gap: 6px;
            font-weight: 600;
            color: #64748b;
            font-size: 12px;
            text-align: center;
            line-height: 1.4;
        }

        .match-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.28);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 20px;
            z-index: 10;
        }

        .overlay-text {
            color: white;
            font-size: 16px;
            font-weight: 700;
            text-align: center;
            padding: 12px 16px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            letter-spacing: 0.5px;
        }

        .no-matches {
            text-align: center;
            padding: 60px 20px;
            background: #f9fafb;
            border-radius: 16px;
            border: 2px dashed #d1d5db;
        }

        .no-matches-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .no-matches-title {
            font-size: 24px;
            font-weight: 700;
            color: #374151;
            margin-bottom: 12px;
        }

        .no-matches-description {
            font-size: 16px;
            color: #6b7280;
            line-height: 1.5;
        }

        /* Responsive Design */
        @media (max-width: 767px) {
            .fawanews-container { padding: 12px 0; }
            .match-card { padding: 12px; margin-bottom: 12px; }
            .match-teams { gap: 8px; }
            .team { max-width: 30%; }
            .team-logo { width: 55px; height: 55px; margin-bottom: 6px; }
            .team-logo img { width: 42px; height: 42px; }
            .team-name { font-size: 11px; line-height: 1.1; }
            .match-center { min-width: 80px; margin: 0 6px; }
            .match-time-center { padding: 8px; min-height: 60px; }
            .center-time { font-size: 14px; }
            .center-status { font-size: 10px; padding: 2px 6px; }
            .tab-item { padding: 8px 12px; font-size: 13px; }
            .tab-bar { padding: 4px; margin-bottom: 16px; }
            .match-info { padding-top: 8px; margin-bottom: 0; }
            .competition { font-size: 10px; }
            .match-card { min-height: 120px; touch-action: manipulation; }
            .match-card:active { transform: scale(0.98); }
            .team-name { word-break: break-word; hyphens: auto; }
            .match-overlay { border-radius: 20px; }
            .overlay-text { font-size: 14px; padding: 8px 12px; }
        }

        @media (min-width: 768px) and (max-width: 1199px) {
            .fawanews-container { padding: 16px 0; }
            .match-card { padding: 14px; }
            .team-logo { width: 65px; height: 65px; }
            .team-logo img { width: 50px; height: 50px; }
            .match-center { min-width: 90px; margin: 0 10px; }
            .match-time-center { padding: 10px; min-height: 65px; }
            .center-time { font-size: 18px; }
            .team-name { font-size: 14px; }
            .competition { font-size: 11px; }
        }

        @media (min-width: 1200px) {
            .match-card { padding: 20px; }
            .team-logo { width: 80px; height: 80px; }
            .team-logo img { width: 65px; height: 65px; }
            .center-time { font-size: 22px; }
            .competition { font-size: 13px; }
        }
        </style>

        <div class="fawanews-container" data-plugin-version="<?php echo FAWANEWS_VERSION; ?>">
            <div class="loading-info" id="fawanewsLoadingInfo">
                <div class="loader-spinner"></div>
                <p>Loading matches...</p>
            </div>

            <div class="tab-bar" id="fawanewsTabBar" style="display: none;">
                <div class="tab-item" data-filter="yesterday">
                    <span class="tab-text">Yesterday</span>
                </div>
                <div class="tab-item active" data-filter="today">
                    <span class="tab-text">Today</span>
                </div>
                <div class="tab-item" data-filter="tomorrow">
                    <span class="tab-text">Tomorrow</span>
                </div>
            </div>

            <div id="fawanewsStatus"></div>
            <div id="fawanewsDataContainer" class="data-container" style="display: none;">
                <div id="fawanewsDataContent" class="data-content"></div>
            </div>
        </div>

        <script type="text/javascript">
        // Debug information
        console.log('FawaNews Sports Plugin v<?php echo FAWANEWS_VERSION; ?> loaded - All assets inlined');
        console.log('AJAX URL:', '<?php echo admin_url('admin-ajax.php'); ?>');
        console.log('jQuery loaded:', typeof jQuery !== 'undefined');

        // Create AJAX object with proper WordPress values
        window.fawanews_ajax = {
            ajax_url: '<?php echo admin_url('admin-ajax.php'); ?>',
            nonce: '<?php echo wp_create_nonce('fawanews_nonce'); ?>'
        };
        console.log('FawaNews: AJAX object created with URL:', window.fawanews_ajax.ajax_url);

        // Fallback initialization function
        function initFawaNewsPluginFallback() {
            console.log('FawaNews: Using fallback initialization');

            const loadingInfo = document.getElementById('fawanewsLoadingInfo');
            const statusDiv = document.getElementById('fawanewsStatus');
            const dataContainer = document.getElementById('fawanewsDataContainer');
            const dataContent = document.getElementById('fawanewsDataContent');
            const tabBar = document.getElementById('fawanewsTabBar');

            if (!loadingInfo) {
                console.error('FawaNews: Required DOM elements not found');
                return;
            }

            // Fetch and display matches using proper WordPress AJAX
            async function fetchMatches(filter = 'today') {
                try {
                    console.log('FawaNews: Fetching matches for filter:', filter);
                    console.log('FawaNews: Using AJAX URL:', fawanews_ajax.ajax_url);
                    console.log('FawaNews: Using nonce:', fawanews_ajax.nonce);

                    const response = await fetch(fawanews_ajax.ajax_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'fawanews_get_matches',
                            nonce: fawanews_ajax.nonce,
                            filter: filter
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();
                    console.log('FawaNews: Backend response:', result);

                    // Hide loading
                    loadingInfo.style.display = 'none';

                    if (result.success) {
                        // Show data
                        dataContent.innerHTML = result.data;
                        dataContainer.style.display = 'block';
                        tabBar.style.display = 'flex';
                    } else {
                        // Show error
                        dataContent.innerHTML = result.data;
                        dataContainer.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Fetch error:', error);
                    loadingInfo.style.display = 'none';
                    dataContent.innerHTML = '<div class="no-matches"><div class="no-matches-icon">⚠️</div><div class="no-matches-title">Unable to Load Matches</div><div class="no-matches-description">There was an issue loading the match data. Please try refreshing the page.</div></div>';
                    dataContainer.style.display = 'block';
                }
            }

            // Initialize tabs
            if (tabBar) {
                const tabItems = tabBar.querySelectorAll('.tab-item');
                tabItems.forEach(tab => {
                    tab.addEventListener('click', function() {
                        // Remove active class from all tabs
                        tabItems.forEach(t => t.classList.remove('active'));
                        // Add active class to clicked tab
                        this.classList.add('active');

                        // Get filter and fetch data
                        const filter = this.getAttribute('data-filter');
                        fetchMatches(filter);
                    });
                });
            }

            // Initial load
            fetchMatches('today');
        }

        // Global function for match click handling (fallback)
        window.fawanewsHandleMatchClick = async function(matchUrl, matchId) {
            if (!matchUrl) return;

            const card = document.querySelector(`[data-match-id="${matchId}"]`);
            if (!card) return;

            try {
                // Show loading overlay
                const overlay = card.querySelector('.match-overlay');
                if (overlay) {
                    overlay.style.opacity = '1';
                    overlay.style.visibility = 'visible';
                    overlay.innerHTML = '<div class="overlay-text">Loading stream...</div>';
                }

                // Extract iframe URL using WordPress AJAX
                const formData = new FormData();
                formData.append('action', 'fawanews_extract_iframe');
                formData.append('url', matchUrl);
                formData.append('nonce', fawanews_ajax.nonce);

                const response = await fetch(fawanews_ajax.ajax_url, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success && result.iframeUrl) {
                    // Open iframe URL in new tab
                    window.open(result.iframeUrl, '_blank');
                } else {
                    // Show error message
                    if (overlay) {
                        overlay.innerHTML = '<div class="overlay-text">Stream not available. Please try again later.</div>';
                        setTimeout(() => {
                            overlay.style.opacity = '0';
                            overlay.style.visibility = 'hidden';
                        }, 2000);
                    }
                }
            } catch (error) {
                console.error('Error handling match click:', error);
                const overlay = card.querySelector('.match-overlay');
                if (overlay) {
                    overlay.innerHTML = '<div class="overlay-text">Error loading stream. Please try again.</div>';
                    setTimeout(() => {
                        overlay.style.opacity = '0';
                        overlay.style.visibility = 'hidden';
                    }, 2000);
                }
            }
        };

        // Ensure script runs even if jQuery is loaded later
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking for fawanews_ajax object:', typeof fawanews_ajax !== 'undefined');

            // Try to initialize the plugin
            if (typeof initFawaNewsPlugin === 'function') {
                console.log('FawaNews: Calling initFawaNewsPlugin from shortcode');
                initFawaNewsPlugin();
            } else {
                console.log('FawaNews: initFawaNewsPlugin not available, using fallback...');
                // Use fallback initialization
                initFawaNewsPluginFallback();
            }
        });
        </script>
        <?php
        return ob_get_clean();
    }
    
    public function ajax_get_matches() {
        // Enhanced nonce verification with better error handling
        $nonce = $_POST['nonce'] ?? '';
        if (!wp_verify_nonce($nonce, 'fawanews_nonce')) {
            error_log('FawaNews Plugin: Nonce verification failed. Received: ' . $nonce);
            wp_send_json([
                'success' => false,
                'data' => '<div class="no-matches"><div class="no-matches-icon">🔒</div><div class="no-matches-title">Security Check Failed</div><div class="no-matches-description">Please refresh the page and try again.</div></div>',
                'error' => 'nonce_verification_failed'
            ]);
            return;
        }

        $filter = sanitize_text_field($_POST['filter'] ?? 'today');

        // Debug logging
        error_log("FawaNews Plugin: AJAX request received for filter: " . $filter);
        error_log("FawaNews Plugin: Nonce verified successfully");

        // Use the exact same logic as the working backend.php
        $result = $this->fetchMatchesUsingBackendLogic($filter);

        // Debug the result
        error_log("FawaNews Plugin: Result success: " . ($result['success'] ? 'true' : 'false'));
        if (isset($result['allMatches'])) {
            $totalMatches = 0;
            foreach (['yesterday', 'today', 'tomorrow'] as $day) {
                if (isset($result['allMatches'][$day])) {
                    $totalMatches += count($result['allMatches'][$day]);
                }
            }
            error_log("FawaNews Plugin: Total matches found: " . $totalMatches);
        }

        wp_send_json($result);
    }

    private function fetchMatchesUsingBackendLogic($filter = 'today') {
        // Copy the exact working logic from backend.php
        $targetUrl = 'https://fawanews.co.uk/';
        $targetSelector = '#content > div > div > div > div.nv-content-wrap.entry-content > div.AlbaSportFixture.asp-font';

        $corsProxies = [
            'https://api.codetabs.com/v1/proxy?quest='
        ];

        $hoverTextMapping = [
            "Soon" => "Comming soon",
            "Live" => "live",
            "Show" => "Watch The Match",
            "NotStarted" => "Not started",
            "FinMatch" => "Match has finished",
            "Finished" => "Finished",
            "NoMatches" => "There are no matches Today"
        ];

        try {
            // Set maximum execution time
            set_time_limit(30);

            $html = $this->fetchHtmlFromSource($targetUrl, $corsProxies);
            if (!$html) {
                return $this->createErrorResponse('Unable to fetch data from source');
            }

            $allMatches = $this->parseHtmlContent($html, $targetSelector, $hoverTextMapping);

            return [
                'success' => true,
                'data' => $this->generateMatchCards($allMatches, $filter),
                'allMatches' => $allMatches
            ];

        } catch (Exception $e) {
            error_log("FawaNews Plugin: Backend logic failed: " . $e->getMessage());
            return $this->createErrorResponse($e->getMessage());
        }
    }

    private function fetchHtmlFromSource($targetUrl, $corsProxies) {
        $lastError = '';
        $startTime = time();
        $maxTime = 15; // Maximum 15 seconds total

        // Try cURL direct first (more reliable with better timeout control)
        if ((time() - $startTime) < $maxTime && function_exists('curl_init')) {
            $html = $this->fetchWithCurl($targetUrl);
            if ($html && strlen($html) > 500) {
                return $html;
            }
            $lastError = 'cURL direct fetch failed';
        }

        // Try file_get_contents with context
        if ((time() - $startTime) < $maxTime) {
            $html = $this->fetchWithFileGetContents($targetUrl);
            if ($html && strlen($html) > 500) {
                return $html;
            }
            $lastError = 'Direct file_get_contents failed';
        }

        // Try CORS proxies as fallback
        foreach ($corsProxies as $proxy) {
            if ((time() - $startTime) >= $maxTime) break;

            try {
                $html = $this->fetchWithProxy($proxy, $targetUrl);
                if ($html && strlen($html) > 500) {
                    return $html;
                }
                $lastError = "Proxy $proxy failed";
            } catch (Exception $e) {
                $lastError = "Proxy $proxy error: " . $e->getMessage();
            }
        }

        error_log("WordPress Plugin: All fetch methods failed. Last error: " . $lastError);
        return false;
    }

    private function fetchWithCurl($url) {
        // Use WordPress wp_remote_get instead of raw cURL for better compatibility
        $response = wp_remote_get($url, [
            'timeout' => 15,
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'headers' => [
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
                'Accept-Encoding' => 'gzip, deflate',
                'Connection' => 'keep-alive',
                'Upgrade-Insecure-Requests' => '1'
            ],
            'sslverify' => false
        ]);

        if (is_wp_error($response)) {
            error_log('FawaNews Plugin: wp_remote_get error: ' . $response->get_error_message());
            return false;
        }

        $http_code = wp_remote_retrieve_response_code($response);
        if ($http_code !== 200) {
            error_log('FawaNews Plugin: HTTP error code: ' . $http_code);
            return false;
        }

        return wp_remote_retrieve_body($response);
    }

    private function fetchWithFileGetContents($url) {
        // Use WordPress wp_remote_get as primary method (more reliable than file_get_contents)
        $response = wp_remote_get($url, [
            'timeout' => 10,
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'headers' => [
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
                'Connection' => 'keep-alive',
                'Upgrade-Insecure-Requests' => '1'
            ],
            'sslverify' => false
        ]);

        if (is_wp_error($response)) {
            error_log('FawaNews Plugin: wp_remote_get (file_get_contents fallback) error: ' . $response->get_error_message());
            return false;
        }

        $http_code = wp_remote_retrieve_response_code($response);
        if ($http_code !== 200) {
            error_log('FawaNews Plugin: HTTP error code (file_get_contents fallback): ' . $http_code);
            return false;
        }

        return wp_remote_retrieve_body($response);
    }

    private function fetchWithProxy($proxy, $targetUrl) {
        $url = $proxy . urlencode($targetUrl);

        // Use WordPress wp_remote_get for proxy requests
        $response = wp_remote_get($url, [
            'timeout' => 10,
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'sslverify' => false
        ]);

        if (is_wp_error($response)) {
            error_log('FawaNews Plugin: Proxy request error: ' . $response->get_error_message());
            return false;
        }

        $http_code = wp_remote_retrieve_response_code($response);
        if ($http_code !== 200) {
            error_log('FawaNews Plugin: Proxy HTTP error code: ' . $http_code);
            return false;
        }

        $body = wp_remote_retrieve_body($response);

        if (strpos($proxy, 'allorigins') !== false) {
            $data = json_decode($body, true);
            if (isset($data['contents']) && !empty($data['contents'])) {
                return $data['contents'];
            }
        } else {
            return $body ?: false;
        }

        return false;
    }

    private function parseHtmlContent($html, $targetSelector, $hoverTextMapping) {
        if (!$html || strlen($html) < 100) {
            return ['yesterday' => [], 'today' => [], 'tomorrow' => []];
        }

        // Create DOMDocument and load HTML
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML('<?xml encoding="utf-8" ?>' . $html);
        libxml_clear_errors();

        $xpath = new DOMXPath($dom);

        // Find the main container
        $containerElements = $xpath->query($targetSelector);

        if ($containerElements === false || $containerElements->length === 0) {
            error_log("WordPress Plugin: Target selector not found: " . $targetSelector);
            return ['yesterday' => [], 'today' => [], 'tomorrow' => []];
        }

        $allMatches = ['yesterday' => [], 'today' => [], 'tomorrow' => []];

        foreach ($containerElements as $container) {
            // Look for match elements within the container
            $matchElements = $xpath->query(".//div[contains(@class, 'AF_Event')]", $container);

            if ($matchElements === false || $matchElements->length === 0) {
                continue;
            }

            foreach ($matchElements as $matchEl) {
                $match = $this->extractMatchData($matchEl, $xpath, $hoverTextMapping);
                if ($match) {
                    // Determine which day this match belongs to
                    $day = $this->determineMatchDay($match);
                    $allMatches[$day][] = $match;
                }
            }
        }

        return $allMatches;
    }

    private function extractMatchData($matchEl, $xpath, $hoverTextMapping) {
        $id = uniqid('match_');
        $day = 'today'; // Default day

        return [
            'id' => $id,
            'day' => $day,
            'status' => $this->getMatchStatus($matchEl),
            'startTime' => $matchEl->getAttribute('data-start') ?: '',
            'homeTeam' => $this->extractTeamData($matchEl, $xpath, '.AF_FTeam'),
            'awayTeam' => $this->extractTeamData($matchEl, $xpath, '.AF_STeam'),
            'time' => $this->extractMatchTime($matchEl, $xpath),
            'timezone' => $this->extractTimezone($matchEl, $xpath),
            'competition' => $this->extractCompetition($matchEl, $xpath),
            'matchUrl' => $this->extractMatchUrl($matchEl, $xpath),
            'hoverText' => $this->extractHoverText($matchEl, $xpath, $hoverTextMapping)
        ];
    }

    private function getMatchStatus($matchEl) {
        $classes = $matchEl->getAttribute('class');
        if (strpos($classes, 'live') !== false) return 'live';
        if (strpos($classes, 'finished') !== false) return 'finished';
        if (strpos($classes, 'comming-soon') !== false) return 'coming-soon';
        if (strpos($classes, 'not-started') !== false) return 'not-started';
        return 'unknown';
    }

    private function extractTeamData($matchEl, $xpath, $selector) {
        // Fix XPath selector - remove the dot from CSS class selector
        $classSelector = str_replace('.', '', $selector);
        $teamElements = $xpath->query(".//div[contains(@class, '{$classSelector}')]", $matchEl);

        if ($teamElements === false || $teamElements->length === 0) {
            return ['name' => 'Unknown', 'logo' => ''];
        }

        $teamEl = $teamElements->item(0);
        $nameElements = $xpath->query(".//span[contains(@class, 'AF_TeamName')]", $teamEl);
        $logoElements = $xpath->query(".//img", $teamEl);

        return [
            'name' => ($nameElements !== false && $nameElements->length > 0) ? trim($nameElements->item(0)->textContent) : 'Unknown',
            'logo' => ($logoElements !== false && $logoElements->length > 0) ? $logoElements->item(0)->getAttribute('src') : ''
        ];
    }

    private function extractMatchTime($matchEl, $xpath) {
        $timeElements = $xpath->query(".//div[contains(@class, 'AF_EvTime')]", $matchEl);
        $statusElements = $xpath->query(".//div[contains(@class, 'AF_StaText')]", $matchEl);

        return [
            'scheduled' => ($timeElements !== false && $timeElements->length > 0) ? trim($timeElements->item(0)->textContent) : '',
            'status' => ($statusElements !== false && $statusElements->length > 0) ? trim($statusElements->item(0)->textContent) : ''
        ];
    }

    private function extractTimezone($matchEl, $xpath) {
        $timezoneElements = $xpath->query(".//div[contains(@class, 'asp-city')]", $matchEl);
        return ($timezoneElements !== false && $timezoneElements->length > 0) ? trim($timezoneElements->item(0)->textContent) : 'Unknown';
    }

    private function extractCompetition($matchEl, $xpath) {
        // Look for competition in the AF_Footer section specifically
        $searches = [
            ".//div[@class='AF_Footer']//span[@class='cup asp-flex']",
            ".//div[contains(@class, 'AF_Footer')]//span[contains(@class, 'cup')]",
            ".//div[contains(@class, 'AF_EvInfo')]//span[contains(@class, 'cup')]",
            ".//span[contains(@class, 'cup')]"
        ];

        foreach ($searches as $search) {
            $elements = $xpath->query($search, $matchEl);
            if ($elements !== false && $elements->length > 0) {
                $text = trim($elements->item(0)->textContent);
                if (!empty($text)) {
                    return $text;
                }
            }
        }

        return 'Unknown';
    }

    private function extractMatchUrl($matchEl, $xpath) {
        $linkElements = $xpath->query(".//a[contains(@class, 'AF_EventMask')]", $matchEl);
        return ($linkElements !== false && $linkElements->length > 0) ? $linkElements->item(0)->getAttribute('href') : '';
    }

    private function extractHoverText($matchEl, $xpath, $hoverTextMapping) {
        $hoverElements = $xpath->query(".//div[contains(@class, 'AF_MaskText')]", $matchEl);
        $rawText = ($hoverElements !== false && $hoverElements->length > 0) ? trim($hoverElements->item(0)->textContent) : '';

        // Map the raw text using the mapping
        return isset($hoverTextMapping[$rawText]) ? $hoverTextMapping[$rawText] : $rawText;
    }

    private function determineMatchDay($match) {
        // Simple logic to determine day - can be enhanced
        return 'today'; // For now, put all matches in today
    }

    private function generateMatchCards($allMatches, $filter) {
        $matches = isset($allMatches[$filter]) ? $allMatches[$filter] : [];

        if (empty($matches)) {
            return $this->createEmptyStateMessage($filter);
        }

        $html = '<div class="matches-container">';
        foreach ($matches as $match) {
            $html .= $this->createMatchCard($match);
        }
        $html .= '</div>';

        return $html;
    }

    private function createMatchCard($match) {
        $statusClass = "status-{$match['status']}";
        $overlayText = $match['hoverText'] ?: 'View Match';
        $centerContent = $this->getCenterContent($match);
        $isClickable = $this->isMatchClickable($match['hoverText'], $match['matchUrl']);
        $cursorStyle = $isClickable ? 'cursor: pointer;' : 'cursor: default; opacity: 0.7;';

        $overlayHtml = $isClickable ? "
            <div class=\"match-overlay\">
                <div class=\"overlay-text\">{$overlayText}</div>
            </div>" : '';

        $clickHandler = $isClickable ? "onclick=\"fawanewsHandleMatchClick('{$match['matchUrl']}', {$match['id']})\"" : '';

        return "
        <div class=\"match-card {$statusClass}\" data-match-id=\"{$match['id']}\"
             {$clickHandler} style=\"{$cursorStyle}\">
            {$overlayHtml}

            <div class=\"match-teams\">
                <div class=\"team home-team\">
                    <div class=\"team-logo\">
                        <img src=\"{$match['homeTeam']['logo']}\" alt=\"{$match['homeTeam']['name']}\" onerror=\"this.style.display='none'\">
                    </div>
                    <div class=\"team-name\">{$match['homeTeam']['name']}</div>
                </div>

                <div class=\"match-center\">
                    <div class=\"match-time-center\">
                        <div class=\"center-time\">{$centerContent['time']}</div>
                        <div class=\"center-status {$centerContent['statusClass']}\">{$centerContent['statusText']}</div>
                    </div>
                </div>

                <div class=\"team away-team\">
                    <div class=\"team-logo\">
                        <img src=\"{$match['awayTeam']['logo']}\" alt=\"{$match['awayTeam']['name']}\" onerror=\"this.style.display='none'\">
                    </div>
                    <div class=\"team-name\">{$match['awayTeam']['name']}</div>
                </div>
            </div>

            <div class=\"match-info\">
                <div class=\"competition\">
                    {$match['competition']}
                </div>
            </div>
        </div>";
    }

    private function getCenterContent($match) {
        $hoverText = strtolower($match['hoverText']);
        $statusClass = '';
        $statusText = '';

        if (strpos($hoverText, 'live') !== false || $hoverText === 'watch the match') {
            $statusClass = 'live';
            $statusText = 'Live';
        } elseif (strpos($hoverText, 'finished') !== false || $hoverText === 'match has finished') {
            $statusClass = 'finished';
            $statusText = 'Match Finished';
        } elseif (strpos($hoverText, 'not started') !== false || strpos($hoverText, 'comming soon') !== false) {
            $statusClass = 'upcoming';
            $statusText = 'Upcoming';
        } else {
            $statusClass = 'upcoming';
            $statusText = 'Upcoming';
        }

        return [
            'time' => $match['time']['scheduled'] ?: 'TBD',
            'statusClass' => $statusClass,
            'statusText' => $statusText
        ];
    }

    private function isMatchClickable($hoverText, $matchUrl) {
        return !empty($matchUrl);
    }

    private function createEmptyStateMessage($filter) {
        $messages = [
            'today' => [
                'icon' => '📅',
                'title' => 'No Events Today',
                'description' => 'There are no events scheduled for today. Check other days for upcoming events!'
            ],
            'yesterday' => [
                'icon' => '⏮️',
                'title' => 'No Events Yesterday',
                'description' => 'There were no events yesterday. Check today\'s or tomorrow\'s schedule!'
            ],
            'tomorrow' => [
                'icon' => '⏭️',
                'title' => 'No Events Tomorrow',
                'description' => 'There are no events scheduled for tomorrow yet. Check back later for updates!'
            ]
        ];

        $message = $messages[$filter] ?? $messages['today'];

        return "
        <div class=\"no-matches\">
            <div class=\"no-matches-icon\">{$message['icon']}</div>
            <div class=\"no-matches-title\">{$message['title']}</div>
            <div class=\"no-matches-description\">{$message['description']}</div>
        </div>";
    }

    private function createErrorResponse($errorMessage) {
        return [
            'success' => false,
            'data' => "
            <div class=\"no-matches\">
                <div class=\"no-matches-icon\">⚠️</div>
                <div class=\"no-matches-title\">Unable to Load Matches</div>
                <div class=\"no-matches-description\">{$errorMessage}</div>
            </div>",
            'allMatches' => ['yesterday' => [], 'today' => [], 'tomorrow' => []]
        ];
    }

    public function ajax_debug() {
        // Enhanced debug endpoint to test WordPress plugin functionality
        error_log('FawaNews Plugin: Debug endpoint called');

        // Test the new WordPress-compatible method
        $result = $this->fetchMatchesUsingBackendLogic('today');

        // Get WordPress environment info
        $wp_info = [
            'wp_version' => get_bloginfo('version'),
            'php_version' => PHP_VERSION,
            'wp_remote_get_available' => function_exists('wp_remote_get'),
            'allow_url_fopen' => ini_get('allow_url_fopen') ? 'enabled' : 'disabled',
            'curl_available' => function_exists('curl_init'),
            'dom_available' => class_exists('DOMDocument'),
            'libxml_available' => function_exists('libxml_use_internal_errors'),
            'admin_ajax_url' => admin_url('admin-ajax.php'),
            'plugin_url' => plugin_dir_url(__FILE__),
            'nonce_test' => wp_create_nonce('fawanews_nonce')
        ];

        // Return comprehensive debug info
        wp_send_json([
            'plugin_version' => FAWANEWS_VERSION,
            'plugin_path' => FAWANEWS_PLUGIN_PATH,
            'wordpress_info' => $wp_info,
            'result_success' => $result['success'] ?? false,
            'result_data_length' => isset($result['data']) ? strlen($result['data']) : 0,
            'total_matches' => isset($result['allMatches']) ? array_sum(array_map('count', $result['allMatches'])) : 0,
            'error_message' => $result['success'] ? null : ($result['data'] ?? 'Unknown error'),
            'debug_timestamp' => current_time('mysql'),
            'result_sample' => $result
        ]);
    }

    public function ajax_extract_iframe() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'fawanews_nonce')) {
            wp_die('Security check failed');
        }

        $url = esc_url_raw($_POST['url'] ?? '');

        if (empty($url)) {
            wp_send_json(array(
                'success' => false,
                'iframeUrl' => null,
                'error' => 'URL parameter is required'
            ));
        }

        // Include the iframe extractor
        require_once FAWANEWS_PLUGIN_PATH . 'includes/class-iframe-extractor.php';

        $extractor = new FawaNews_Iframe_Extractor();
        $result = $extractor->extractIframeUrl($url);

        wp_send_json($result);
    }
}

// Initialize the plugin
new FawaNews_Plugin();

// Activation hook
register_activation_hook(__FILE__, 'fawanews_activate');
function fawanews_activate() {
    // Create any necessary database tables or options
    update_option('fawanews_version', FAWANEWS_VERSION);
    // Clear any cached scripts/styles from previous versions
    wp_cache_flush();
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'fawanews_deactivate');
function fawanews_deactivate() {
    // Clean up if necessary
}

// Uninstall hook
register_uninstall_hook(__FILE__, 'fawanews_uninstall');
function fawanews_uninstall() {
    // Remove options and clean up
    delete_option('fawanews_version');
}
